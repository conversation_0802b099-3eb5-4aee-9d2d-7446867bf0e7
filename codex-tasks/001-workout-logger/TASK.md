# Task: Build WorkoutLogger Component

Build a React component for logging workout sets.

## Requirements
- Exercise dropdown (use data/exercises.json)
- Weight input (0.25 lb increments, max 500)
- Reps input (1-50 range)
- Form validation with react-hook-form + zod
- Save sets via POST /api/workout-sets
- TypeScript

## Files to create
- `deliverables/WorkoutLogger.tsx`
- `deliverables/useWorkoutLogger.ts` 
- `deliverables/validation.ts`

That's it. Build it however you think is best.