# FitForge Backend Dependencies
# FastAPI and core dependencies

# FastAPI framework and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Pydantic for data validation and settings
pydantic==2.5.0
pydantic-settings==2.1.0

# Database dependencies
sqlalchemy==2.0.23
asyncpg==0.29.0
alembic==1.13.0

# Supabase client (optional)
supabase==2.0.1
postgrest==0.13.0

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# HTTP client for external APIs - compatible with supabase requirements
httpx==0.24.1
aiohttp==3.9.1

# Redis for caching and sessions (optional)
redis==5.0.1
aioredis==2.0.1

# Data processing and analytics
pandas==2.1.4
numpy==1.24.4
scipy==1.11.4

# Logging and monitoring
python-json-logger==2.0.7
structlog==23.2.0

# Development and testing dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
# httpx==0.24.1 already defined above for compatibility

# Code quality and formatting
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Environment and configuration
python-dotenv==1.0.0

# Date and time utilities
python-dateutil==2.8.2

# Validation and parsing utilities
email-validator==2.1.0

# UUID utilities - built-in Python module, no need to install
# uuid==1.30

# Math and decimal handling - built-in Python module, no need to install  
# decimal==1.70

# Type hints for older Python versions
typing-extensions==4.8.0

# CORS middleware (included in FastAPI but listed for clarity)
# fastapi already includes starlette which provides CORS

# Optional: Machine learning for advanced analytics
# scikit-learn==1.3.2
# tensorflow==2.15.0  # Uncomment if using ML features

# Optional: Image processing for user avatars
# Pillow==10.1.0  # Uncomment if handling image uploads

# Optional: Background task processing
# celery==5.3.4
# kombu==5.3.4

# Optional: Monitoring and metrics
# prometheus-client==0.19.0
# opencensus-ext-azure==1.1.13

# Production deployment
gunicorn==21.2.0

# Health checks and utilities
psutil==5.9.6