[tool:pytest]
# FitForge Backend Test Configuration
minversion = 6.0
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --strict-markers
    --tb=short
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=80
    --asyncio-mode=auto
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    security: marks tests as security-related
    regression: marks tests as regression prevention
asyncio_mode = auto
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning