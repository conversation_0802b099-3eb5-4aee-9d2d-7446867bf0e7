<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

# Fitness App Research: User Pain Points, Retention, and Tracking Preferences

Based on comprehensive research across app store reviews, user forums, and fitness communities, here are the key findings for each research question:

## Question 1: User Pain Points with Popular Fitness Apps

### MyFitnessPal

The most frequent complaints center around **pricing and feature restrictions** [^1][^2][^3]. Users express frustration that the barcode scanning feature was moved to premium, with many calling this a "money grab" [^2][^3]. Common complaints include:

- **Subscription costs**: Users describe \$20/month as excessive, comparing it unfavorably to Disney+ pricing [^3]
- **Account access issues**: Persistent login problems and password reset failures [^4][^5]
- **Limited free features**: Only 5 food selections shown in searches, requiring multiple clicks for more options [^1]
- **Customer service**: Described as "LOUSY" with form email responses and no phone support [^3][^5]


### Strong App

Users report **technical reliability issues** as the primary concern [^6]:

- **Frequent crashes**: App crashes repeatedly, requiring periodic reinstallation [^7]
- **Outdated interface**: Users describe it as "clunky" and "dated" despite functionality [^6]
- **Limited updates**: Minimal development progress frustrates long-term users [^6]


### Jefit

The app suffers from **overwhelming complexity without guidance** [^8]:

- **Poor program quality**: Generic, uninspired workout programs with "critical programming errors" [^8]
- **Lack of personalization**: Difficulty finding programs suited to specific needs and goals [^8]
- **Program hopping**: Too many options encourage ineffective switching between routines [^8]


### Strava

User feedback reveals **subscription and technical frustrations** [^9]:

- **Pricing complaints**: Users frustrated with subscription costs and pricing strategies [^9]
- **Location tracking issues**: "Not even remotely accurate" GPS tracking [^9]
- **Interface problems**: Described as "glitchy" with poor customer support [^9]


## Question 2: Excel vs Apps - The DIY Preference

**Yes, the Excel users absolutely exist** and represent a significant segment of serious fitness enthusiasts. Their motivations are surprisingly sophisticated:

### Key Advantages of Spreadsheets

**Complete customization and control** [^10][^11]:

- "Far more options on what and how you're able to track" compared to rigid app structures [^10]
- Ability to create custom formulas for tracking volume, intensity, and progression metrics [^12]

**Superior data visualization** [^10][^13]:

- "Quickly and easily create graphs and charts to really see what's up" [^10]
- Custom analytics that apps don't provide, like total weight lifted over time [^10]

**Permanent data ownership** [^10][^11]:

- Information "saved forever" in cloud storage with 30-day recovery options [^10]
- No risk of losing data if an app shuts down or changes policies [^11]

**Accessibility and sharing** [^10][^11]:

- Access from any device with internet connection [^10]
- Easy sharing with coaches and training partners [^11]
- Works offline unlike many apps [^10]


### Specific User Language

Users describe spreadsheets as providing a "multiplier to my fitness growth" and enabling them to "track progress better than any workout app" [^13]. The community particularly values being able to "see all results on one page" rather than navigating through multiple app screens [^13].

## Question 3: Retention \& Stickiness Factors

### What Makes Users Stick (Long-term Retention)

**Personalization and adaptability** emerge as the strongest retention factors [^14][^15]:

- Apps that adapt to user goals, fitness levels, and preferences show higher retention [^16][^15]
- "Task Technology Fit" - alignment between app features and user goals - is critical [^15]

**Social features and community** [^17][^18]:

- **96% of fitness app users stick to only one app**, showing high loyalty when engaged [^19][^20]
- Community challenges and social interaction can boost session duration by 30% [^21]
- **75% of active users open their app at least twice weekly**, with 25% using it over 10 times per week [^19][^20]

**Progressive feedback and recognition** [^17][^22]:

- Streak tracking and achievement recognition motivate continued use [^17][^23]
- Visual progress indicators and celebration of small wins create positive feedback loops [^17][^22]


### What Causes Abandonment

The data reveals concerning retention statistics: **fitness apps have only 3-8% retention by day 30** [^24][^25], with some studies showing **71% of users abandon apps within 3 months** [^26][^27].

**Primary abandonment reasons** [^16][^26][^27]:

- **Lack of immediate results**: Unrealistic expectations for quick physical changes [^26]
- **Poor onboarding**: Overwhelming or confusing initial experiences [^16]
- **Generic programming**: One-size-fits-all approaches that don't feel personalized [^16][^26]
- **Technical issues**: Crashes, bugs, and sync problems drive users away [^27]
- **Overwhelming complexity**: Too many features without clear guidance [^26]


## Question 4: Gamification Reality Check

### Successful Gamification Elements

**Meaningful progress tracking** resonates most with users [^23][^28]:

- **Badges for specific achievements** (like "10,000 steps" or "first 5K") feel earned and motivating [^23][^29]
- **Streak tracking** works well when it allows for rest days and doesn't punish breaks [^17]
- **Leaderboards** create "magnetic sense of social connection" when done with friends [^30]

**Goal-setting and challenges** show strong user engagement [^28][^29]:

- 78% of gamified fitness apps use goal-setting and social influences effectively [^28]
- Team challenges and collaborative goals build community [^29][^31]


### Failed/Annoying Gamification

**Forced interactions and gimmicky mechanics** frustrate users [^32]:

- **Shake-to-activate features**: Users report feeling "dumb" having to shake phones in public [^32]
- **Over-reliance on external rewards**: Can reduce intrinsic motivation over time [^33]
- **Overly competitive elements**: Some users find constant competition "demotivating or stressful" [^33]
- **Short-term focus**: Gamification that prioritizes immediate rewards over long-term behavior change [^33]

Research shows that **64% of fitness apps use gamification**, but **none incorporated behavioral economics principles** like loss aversion or variable reinforcement that could make rewards more effective [^28].

## Question 5: Progressive Overload \& Analytics Tracking

### Serious Lifters' Preferred Tools

**App preferences** among dedicated strength athletes [^34][^35][^36]:

- **Hevy**: Popular for its social features and free tier [^34][^37][^38]
- **Strong**: Preferred despite bugs for its simplicity [^34][^7]
- **Boostcamp**: Valued for pre-made programs and free access [^34][^36]
- **Setgraph**: Emerging preference for "lightning-fast interface" and analytics focus [^35][^39]

**Spreadsheet dominance** among serious practitioners [^40][^34][^13]:

- Google Sheets remains popular for "infinite customization" [^13]
- Excel users create sophisticated tracking with "percentage calculations" and progression formulas [^12]
- Many users maintain both: apps for daily logging, spreadsheets for analysis [^34]


### Key Tracking Metrics

**Traditional metrics** still dominate [^41][^42]:

- **Load, volume (sets x reps), rest periods** form the foundation [^42]
- **Rate of Perceived Exertion (RPE)** for subjective intensity tracking [^42]
- **Progressive overload tracking** through systematic weight/rep increases [^41][^43]

**Advanced analytics** desired by serious users [^44][^39]:

- **Weekly volume trends** to manage fatigue and prevent overtraining [^39]
- **Strength ratios** and bodyweight comparisons for efficiency tracking [^44]
- **Success rate analysis** for different rep ranges and intensities [^44]


## Key Insights and Unexpected Findings

### Validation of "Gamer/Min-maxer" Intuition

The research **strongly validates** the gamer/optimization mindset among serious users:

- Spreadsheet users demonstrate clear "min-maxer" behavior with custom formulas and detailed analytics [^12][^13]
- Serious lifters want "data-driven insights" and "precision tracking" [^39][^43]
- Community discussions show users who "track everything" and optimize based on metrics [^34]


### Challenging Assumptions

**Progressive information disclosure** may be more critical than initially assumed:

- Users abandon apps due to "overwhelming complexity" more than lack of features [^26]
- The most successful apps balance simplicity with depth [^15][^35]
- **Ease of use is paramount** - even advanced users prefer "lightning-fast interfaces" over feature bloat [^35][^22]


### The "Delightful, Non-intimidating Interface" Opportunity

Current market gaps suggest significant opportunity:

- Most apps suffer from either oversimplification OR overwhelming complexity [^16][^8]
- Users want **customization without complexity** - the ability to access advanced features without mandatory exposure [^15]
- **Social features** are valued but must feel authentic, not forced [^17][^31]

The research reveals a clear market need for an app that serves both casual users (through progressive disclosure) and serious athletes (through deep customization), without forcing either group into the other's preferred interaction model.

<div style="text-align: center">⁂</div>

[^1]: https://www.reddit.com/r/Myfitnesspal/comments/1dcd22o/what_are_your_biggest_complaints_about_mfp/

[^2]: https://www.choosingtherapy.com/myfitnesspal-review/

[^3]: https://www.sitejabber.com/reviews/myfitnesspal.com

[^4]: https://www.bbb.org/us/ca/san-francisco/profile/online-shopping/myfitnesspal-1116-539525/complaints

[^5]: https://myfitnesspal.pissedconsumer.com/reviews/RT-P.html

[^6]: https://dr-muscle.com/strong-workout-app-review/

[^7]: https://www.reddit.com/r/naturalbodybuilding/comments/1jkbsxq/what_annoys_you_most_about_apps_for_tracking/

[^8]: https://dr-muscle.com/jefit-workout-app-review/

[^9]: https://kimola.com/reports/unlock-insights-strava-app-user-feedback-report-app-store-us-151062

[^10]: https://stayfitcentral.com/why-spreadsheets-workout-trackers-are-better-than-paper/

[^11]: https://stayfitcentral.com/why-and-how-you-should-record-workouts-with-a-spreadsheet/

[^12]: https://gymtalk.com/bodybuilding-and-microsoft-excel-part-1-1rm-percentages/

[^13]: https://stayfitcentral.com/track-your-strength-gains-with-a-spreadsheet/

[^14]: https://pmc.ncbi.nlm.nih.gov/articles/PMC9517841/

[^15]: https://ro.ecu.edu.au/ecuworks2022-2026/6150/

[^16]: https://imaginovation.net/blog/why-fitness-apps-lose-users-ai-ar-gamification-fix/

[^17]: https://orangesoft.co/blog/strategies-to-increase-fitness-app-engagement-and-retention

[^18]: https://clevertap.com/blog/fitness-apps-retain-new-users/

[^19]: https://appdevelopermagazine.com/health-and-fitness-apps-usage-report-from-flurry/

[^20]: https://www.flurry.com/blog/health-fitness-app-users-are-going-the-distance/

[^21]: https://www.sportfitnessapps.com/blog/top-7-user-behavior-metrics-for-fitness-apps

[^22]: https://pmc.ncbi.nlm.nih.gov/articles/PMC11537609/

[^23]: https://www.plotline.so/blog/gamification-in-health-and-fitness-apps

[^24]: https://www.businessofapps.com/data/health-fitness-app-benchmarks/

[^25]: https://www.statista.com/statistics/259329/ios-and-android-app-user-retention-rate/

[^26]: https://autentika.com/blog/why-do-users-abandon-fitness-apps

[^27]: https://www.jmir.org/2024/1/e56897/

[^28]: https://pmc.ncbi.nlm.nih.gov/articles/PMC6348030/

[^29]: https://theinscribermag.com/how-gamification-enhances-health-and-fitness-apps-8-effective-approaches/

[^30]: https://strivecloud.io/blog/gamification-features-mhealth/

[^31]: https://www.myfitapp.com/blog/gamification-in-fitness-apps/

[^32]: https://uxplanet.org/gamification-is-dangerous-and-heres-why-d0a3622e0951?gi=7f03962cced5

[^33]: https://dialnet.unirioja.es/descarga/articulo/9056526.pdf

[^34]: https://www.reddit.com/r/naturalbodybuilding/comments/1be22g3/how_do_you_track_your_progressive_overload_in_the/

[^35]: https://setgraph.app/articles/what-is-the-best-app-for-tracking-workouts

[^36]: https://www.garagestrength.com/blogs/news/best-weightlifting-apps

[^37]: https://www.reddit.com/r/workout/comments/1ipbqiy/looking_for_a_fitness_app_what_is_everyones/

[^38]: https://www.garagegymreviews.com/best-weightlifting-app

[^39]: https://setgraph.app/articles/setgraph-the-best-workout-tracker-for-weight-lifters

[^40]: https://www.reddit.com/r/weightlifting/comments/9ikzav/does_anyone_have_an_excel_spreadsheet_they_use_to/

[^41]: https://fivestarrphysique.com/beginning-bodybuilding/effectively-logging-your-workouts/

[^42]: https://www.technogym.com/us/newsroom/metrics-strength-training-drive-success/

[^43]: https://setgraph.app/articles/master-progressive-overload-for-muscle-growth

[^44]: https://plfocus.com/sports-data-analytics-in-powerlifting-unlocking-performance-insights/

[^45]: https://support.myfitnesspal.com/hc/en-us/articles/360032625451-Known-Issues-Website

[^46]: https://cs.uwaterloo.ca/~m2nagapp/publications/pdfs/What-Do-Mobile-App-Users-Complain-About-A-Study-on-Free-iOS-Apps.pdf

[^47]: https://www.youtube.com/watch?v=Y7onlEJXxic

[^48]: https://clickup.com/blog/exercise-log-templates/

[^49]: https://support.microsoft.com/en-us/office/track-your-health-and-fitness-goals-in-excel-322af086-734b-4837-ba4f-410c6bceccd4

[^50]: https://www.mychoicesoftware.com/blogs/news/track-your-fitness-in-excel-using-office-365

[^51]: https://valadilene.org/google-sheets-workout-template/

[^52]: https://yoogoods.com/crafting-diy-wearable-fitness-trackers-monitor-your-health-in-style/

[^53]: https://www.hubsite365.com/en-ww/crm-pages/excel-workout-tracker-2025-build-a-workout-calendar-in-excel-babd7fb3-0773-4216-bc3c-c4a152ce04b9.htm

[^54]: https://stormotion.io/blog/how-mobile-apps-help-to-increase-reach-retention-engagement-in-the-fitness-industry/

[^55]: https://www.productmarketingalliance.com/7-strategies-b2c-fitness-apps-use-to-increase-customer-engagement/

[^56]: https://yukaichou.com/gamification-examples/fitness-gamification-examples/

[^57]: https://www.hevyapp.com/progressive-overload/

[^58]: https://gymaware.com/progressive-overload-the-ultimate-guide/

[^59]: https://www.washingtonian.com/2018/10/30/how-i-got-this-body-lifting-weights-and-using-excel-spreadsheets-to-track-progress/

[^60]: https://www.youtube.com/watch?v=56a24XOFfAM

[^61]: https://help.count.it/en/articles/8774807-how-do-i-turn-on-or-off-manual-entries

[^62]: https://diyjoy.com/diy-exercise-equipment/

[^63]: https://civilizedcaveman.com/wellness/how-to-build-your-own-health-tracker-using-google-sheets/

[^64]: https://www.businessofapps.com/data/health-fitness-app-report/

[^65]: https://pmc.ncbi.nlm.nih.gov/articles/PMC11420572/

[^66]: https://www.reddit.com/r/Fitness/

[^67]: https://gummysearch.com/tools/best-products/fitness-app/

[^68]: https://www.wada-ama.org/sites/default/files/resources/files/wada-davis-internet-surveillance.pdf

[^69]: https://www.reddit.com/r/powerlifting/comments/aw04t0/new_to_the_sub_start_here/

[^70]: https://chord.pub/article/51782/problems-with-fitness-apps

[^71]: https://gamifylist.com/goal/health

[^72]: https://www.pastemagazine.com/tech/apps/10-great-apps-to-help-gamify-your-workout

[^73]: https://www.nourishmovelove.com/progressive-overload-workout-plan/

[^74]: https://www.reddit.com/r/WorkoutRoutines/comments/1htvfm1/do_you_use_an_app_for_your_workouts_whats_your/

