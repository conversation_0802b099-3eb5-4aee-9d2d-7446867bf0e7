{"permissions": {"allow": ["mcp__sequential-thinking__sequentialthinking", "Bash(grep:*)", "Bash(ls:*)", "Bash(find:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__notion__API-post-page", "Bash(./start-fitforge-docker.sh:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(./start-fitforge-v2-dev.sh:*)", "Bash(sudo chmod:*)"], "deny": []}}