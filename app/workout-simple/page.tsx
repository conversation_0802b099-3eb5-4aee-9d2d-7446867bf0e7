'use client';

import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { WorkoutLogger } from '@/components/WorkoutLogger';

export default function WorkoutSimplePage() {
  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">Simple Workout Logger</h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6">
          <WorkoutLogger 
            userId="demo-user"
            onSetCompleted={(set) => {
              console.log('Set completed:', set);
            }}
            onSessionEnd={(session) => {
              console.log('Session ended:', session);
            }}
          />
        </div>
        
        <div className="mt-8 text-center text-sm text-gray-600">
          <p>This page tests the WorkoutLogger component with the full exercise database.</p>
          <p>Data is saved to localStorage and should appear in the Dashboard.</p>
        </div>
      </div>
    </div>
  );
}
