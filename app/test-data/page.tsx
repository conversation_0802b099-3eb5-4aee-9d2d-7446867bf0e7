'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestDataPage() {
  const addSampleWorkoutData = () => {
    // Sample workout sessions with progression data
    const sampleSessions = [
      {
        id: 'sample-1',
        name: 'Push Day - Week 1',
        date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
        duration: 60,
        exercises: [
          { id: 'bench_press', name: 'Bench Press', sets: 3 },
          { id: 'shoulder_press', name: 'Shoulder Press', sets: 3 },
          { id: 'pushup', name: 'Pushup', sets: 2 }
        ],
        totalSets: 8,
        sets: [
          { exerciseId: 'bench_press', weight: 135, reps: 8 },
          { exerciseId: 'bench_press', weight: 135, reps: 8 },
          { exerciseId: 'bench_press', weight: 135, reps: 7 },
          { exerciseId: 'shoulder_press', weight: 65, reps: 8 },
          { exerciseId: 'shoulder_press', weight: 65, reps: 8 },
          { exerciseId: 'shoulder_press', weight: 65, reps: 8 },
          { exerciseId: 'pushup', weight: 0, reps: 15 },
          { exerciseId: 'pushup', weight: 0, reps: 12 }
        ]
      },
      {
        id: 'sample-2',
        name: 'Pull Day - Week 1',
        date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
        duration: 55,
        exercises: [
          { id: 'bent_over_rows', name: 'Bent Over Rows', sets: 3 },
          { id: 'chin_ups', name: 'Chin-Ups', sets: 3 },
          { id: 'concentration_curl', name: 'Concentration Curl', sets: 2 }
        ],
        totalSets: 8,
        sets: [
          { exerciseId: 'bent_over_rows', weight: 95, reps: 8 },
          { exerciseId: 'bent_over_rows', weight: 95, reps: 8 },
          { exerciseId: 'bent_over_rows', weight: 95, reps: 8 },
          { exerciseId: 'chin_ups', weight: 0, reps: 6 },
          { exerciseId: 'chin_ups', weight: 0, reps: 5 },
          { exerciseId: 'chin_ups', weight: 0, reps: 4 },
          { exerciseId: 'concentration_curl', weight: 25, reps: 12 },
          { exerciseId: 'concentration_curl', weight: 25, reps: 10 }
        ]
      },
      {
        id: 'sample-3',
        name: 'Legs Day - Week 1',
        date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
        duration: 70,
        exercises: [
          { id: 'goblet_squats', name: 'Goblet Squats', sets: 3 },
          { id: 'dead_lifts', name: 'Dead Lifts', sets: 3 },
          { id: 'calf_raises', name: 'Calf Raises', sets: 2 }
        ],
        totalSets: 8,
        sets: [
          { exerciseId: 'goblet_squats', weight: 45, reps: 10 },
          { exerciseId: 'goblet_squats', weight: 45, reps: 10 },
          { exerciseId: 'goblet_squats', weight: 45, reps: 9 },
          { exerciseId: 'dead_lifts', weight: 155, reps: 8 },
          { exerciseId: 'dead_lifts', weight: 155, reps: 8 },
          { exerciseId: 'dead_lifts', weight: 155, reps: 8 },
          { exerciseId: 'calf_raises', weight: 35, reps: 15 },
          { exerciseId: 'calf_raises', weight: 35, reps: 15 }
        ]
      }
    ];

    // Add to localStorage
    const existingSessions = JSON.parse(localStorage.getItem('workoutSessions') || '[]');
    const updatedSessions = [...existingSessions, ...sampleSessions];
    localStorage.setItem('workoutSessions', JSON.stringify(updatedSessions));
    
    alert('Sample workout data added! Now you can see progressive overload recommendations.');
  };

  const clearAllData = () => {
    localStorage.removeItem('workoutSessions');
    localStorage.removeItem('currentWorkoutSession');
    alert('All workout data cleared!');
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">Test Data Manager</h1>
        
        <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Add Sample Data</CardTitle>
              <CardDescription>
                Add sample workout sessions to test progressive overload features
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={addSampleWorkoutData} className="w-full">
                Add Sample Workout Data
              </Button>
              <div className="mt-4 text-sm text-gray-600">
                <p>This will add 3 sample workouts:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Push Day (Bench Press, Shoulder Press, Pushups)</li>
                  <li>Pull Day (Rows, Chin-ups, Curls)</li>
                  <li>Legs Day (Squats, Deadlifts, Calf Raises)</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Clear Data</CardTitle>
              <CardDescription>
                Remove all workout data from localStorage
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={clearAllData} variant="destructive" className="w-full">
                Clear All Data
              </Button>
              <div className="mt-4 text-sm text-gray-600">
                <p>This will remove:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>All completed workout sessions</li>
                  <li>Current workout session</li>
                  <li>All progression data</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="mt-8 text-center">
          <p className="text-gray-600">
            After adding sample data, go to the Simple Logger to see progressive overload recommendations!
          </p>
        </div>
      </div>
    </div>
  );
}
