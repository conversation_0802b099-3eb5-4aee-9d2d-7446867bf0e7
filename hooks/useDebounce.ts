/**
 * Debounce Hook
 * Delays updating a value until after a specified delay
 */

import { useState, useEffect } from 'react'

export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    // Set up the timeout
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    // Clean up the timeout if value changes (or component unmounts)
    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}