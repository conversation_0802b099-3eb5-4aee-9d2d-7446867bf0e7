{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "echo '🚨 DEPRECATED: Use ./start-fitforge-docker.sh instead!' && echo '🐳 Docker is the ONLY supported development method!' && exit 1", "dev:next": "next dev", "dev:docker": "docker-compose -f docker-compose.fast.yml up --build -d", "dev:stop": "docker-compose -f docker-compose.fast.yml down", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:e2e": "playwright test", "test:fast": "playwright test --config=playwright.fast.config.ts", "test:fast:headed": "playwright test --config=playwright.fast.config.ts --headed", "test:performance": "jest tests/performance", "test:docker": "jest tests/docker", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "npm run test:all && npm run test:performance", "test:self-improve": "bash scripts/self-improving-test-cycle.sh"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@supabase/supabase-js": "^2.50.0", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "8.5.1", "html2canvas": "^1.4.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "^15.3.3", "next-themes": "latest", "puppeteer": "^24.10.2", "react": "^18.3.0", "react-day-picker": "8.10.1", "react-dom": "^18.3.0", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "react-virtualized-auto-sizer": "^1.0.24", "react-window": "^1.8.10", "recharts": "latest", "sonner": "^1.7.4", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.6", "zod": "^3.25.56"}, "devDependencies": {"@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.5.0", "@types/jest": "^29.5.14", "@types/node": "^22", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@types/react-window": "^1.8.8", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "msw": "^2.0.0", "postcss": "^8.5", "tailwindcss": "^3.4.17", "ts-jest": "^29.1.0", "typescript": "^5"}}