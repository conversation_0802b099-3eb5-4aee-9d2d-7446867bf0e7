'use client'

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Activity, Calendar, Du<PERSON>bell, TrendingUp, Clock } from "lucide-react"

interface WorkoutSession {
  id: string
  name: string
  date: string
  duration: number
  exercises: any[]
  totalSets: number
}

interface WeeklyStats {
  workouts: number
  totalTime: number
  exercisesCompleted: number
  avgIntensity: number
}

export function DashboardSimple() {
  const [recentWorkouts, setRecentWorkouts] = useState<WorkoutSession[]>([])
  const [weeklyStats, setWeeklyStats] = useState<WeeklyStats>({
    workouts: 0,
    totalTime: 0,
    exercisesCompleted: 0,
    avgIntensity: 0,
  })

  useEffect(() => {
    const loadDashboardData = () => {
      try {
        // Load workouts from localStorage
        const workouts = JSON.parse(localStorage.getItem("workoutSessions") || "[]")
        const recent = workouts.slice(-5).reverse()
        setRecentWorkouts(recent)

        // Calculate weekly stats
        const weekAgo = new Date()
        weekAgo.setDate(weekAgo.getDate() - 7)
        
        const thisWeek = workouts.filter((w: WorkoutSession) => {
          return new Date(w.date) >= weekAgo
        })

        const stats = {
          workouts: thisWeek.length,
          totalTime: thisWeek.reduce((sum: number, w: WorkoutSession) => sum + w.duration, 0),
          exercisesCompleted: thisWeek.reduce((sum: number, w: WorkoutSession) => sum + (w.exercises?.length || 0), 0),
          avgIntensity: thisWeek.length > 0
            ? Math.round(thisWeek.reduce((sum: number, w: WorkoutSession) => sum + w.totalSets, 0) / thisWeek.length)
            : 0,
        }
        setWeeklyStats(stats)
      } catch (error) {
        console.error("Error loading dashboard data:", error)
      }
    }

    loadDashboardData()
    // Refresh every 2 seconds
    const interval = setInterval(loadDashboardData, 2000)
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="space-y-8" data-testid="app-loaded">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">Your fitness journey at a glance</p>
      </div>

      {/* Weekly Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Workouts This Week</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{weeklyStats.workouts}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{weeklyStats.totalTime} min</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Exercises Done</CardTitle>
            <Dumbbell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{weeklyStats.exercisesCompleted}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Sets/Workout</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{weeklyStats.avgIntensity}</div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Workouts */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Workouts</CardTitle>
          <CardDescription>Your last 5 workout sessions</CardDescription>
        </CardHeader>
        <CardContent>
          {recentWorkouts.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">No workouts logged yet</p>
              <Button onClick={() => window.location.href = "/test-logger"}>
                Log Your First Workout
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {recentWorkouts.map((workout) => (
                <div key={workout.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-semibold">{workout.name}</h4>
                    <p className="text-sm text-muted-foreground">
                      {new Date(workout.date).toLocaleDateString()} • {workout.duration} min
                    </p>
                  </div>
                  <div className="flex items-center gap-4">
                    <Badge variant="secondary">
                      {workout.totalSets} sets
                    </Badge>
                    <Badge>
                      {workout.exercises.length} exercises
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="flex gap-4">
        <Button onClick={() => window.location.href = "/test-logger"}>
          Start New Workout
        </Button>
        <Button variant="outline" onClick={() => window.location.href = "/minimal"}>
          Try Minimal Demo
        </Button>
      </div>
    </div>
  )
}